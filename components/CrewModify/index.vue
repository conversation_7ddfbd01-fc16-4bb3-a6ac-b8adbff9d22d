<template>
  <view class="crew-modify">
    <view>
      <view
        class="section-title"
        :class="
          getDepartmentPermissions('1').canEdit ? '' : 'section-title-disabled'
        "
      >
        {{ getSectionTitle('飞行部') }}
      </view>
      <van-row>
        <van-col span="14">
          <FormItem label="机长：" label-width="70px">
            <input
              class="input-box"
              v-model="formNameData.crewRole1"
              :placeholder="
                getDepartmentPermissions('1').canEdit ? '请选择' : ''
              "
              disabled
              @click="openPicker('机长类型', 'pilotType1', 'crewRole1', '1')"
            />
          </FormItem>
        </van-col>
        <van-col span="10">
          <FormItem label-width="0px">
            <input
              class="input-box"
              v-model="formNameData.captainId"
              :placeholder="
                getDepartmentPermissions('1').canEdit ? '请选择' : ''
              "
              disabled
              @click="openPicker('机长', 'pilot', 'captainId', '1')"
            />
          </FormItem>
        </van-col>
      </van-row>
      <van-row>
        <van-col span="14">
          <FormItem :label="getSubLabel('副机长')" label-width="70px">
            <input
              v-model="formNameData.crewRole2"
              :placeholder="
                getDepartmentPermissions('1').canEdit ? '请选择' : ''
              "
              disabled
              @click="
                openPicker(
                  getSubPickerTitle('副驾驶'),
                  'pilotType2',
                  'crewRole2',
                  '1'
                )
              "
            />
          </FormItem>
        </van-col>
        <van-col span="10">
          <FormItem label-width="0px">
            <input
              v-model="formNameData.copilotId"
              :placeholder="
                getDepartmentPermissions('1').canEdit ? '请选择' : ''
              "
              disabled
              @click="openPicker('副机长', 'pilot', 'copilotId', '1')"
            />
          </FormItem>
        </van-col>
      </van-row>
      <FormItem v-if="showRemarks" label="备注：" label-width="70px">
        <input
          v-model="formIdData.flightRemark"
          :placeholder="getDepartmentPermissions('1').canEdit ? '请输入' : ''"
          :disabled="!getDepartmentPermissions('1').canEdit"
        />
      </FormItem>
    </view>

    <view>
      <view
        class="section-title"
        :class="
          getDepartmentPermissions('2').canEdit ? '' : 'section-title-disabled'
        "
        >{{ getSectionTitle('机务部') }}
      </view>
      <FormItem label="放行：" label-width="70px">
        <input
          v-model="formNameData.maintenanceId"
          :placeholder="getDepartmentPermissions('2').canEdit ? '请选择' : ''"
          disabled
          @click="openPicker('放行', 'maintenance', 'maintenanceId', '2')"
        />
      </FormItem>
      <FormItem label="机械员：" label-width="70px">
        <input
          v-model="formNameData.mechanicId"
          :placeholder="getDepartmentPermissions('2').canEdit ? '请选择' : ''"
          disabled
          @click="openPicker('机械员', 'maintenance', 'mechanicId', '2')"
        />
      </FormItem>
      <FormItem v-if="showRemarks" label="备注：" label-width="70px">
        <input
          v-model="formIdData.maintenanceRemark"
          :placeholder="getDepartmentPermissions('2').canEdit ? '请输入' : ''"
          :disabled="!getDepartmentPermissions('2').canEdit"
        />
      </FormItem>
    </view>

    <view>
      <view
        class="section-title"
        :class="
          getDepartmentPermissions('3').canEdit ? '' : 'section-title-disabled'
        "
        >{{ getSectionTitle('运控中心') }}
      </view>
      <FormItem label="安检：" label-width="70px">
        <input
          v-model="formNameData.inspectorId"
          :placeholder="getDepartmentPermissions('3').canEdit ? '请选择' : ''"
          disabled
          @click="openPicker('安检', 'operationControl', 'inspectorId', '3')"
        />
      </FormItem>
      <FormItem label="现场组织：" label-width="70px">
        <input
          v-model="formNameData.organizationId"
          :placeholder="getDepartmentPermissions('3').canEdit ? '请选择' : ''"
          disabled
          @click="
            openPicker('现场组织', 'operationControl', 'organizationId', '3')
          "
        />
      </FormItem>
      <FormItem label="责任运控：" label-width="70px">
        <input
          v-model="formNameData.ocUserId"
          :placeholder="getDepartmentPermissions('3').canEdit ? '请选择' : ''"
          disabled
          @click="openPicker('责任运控', 'operationControl', 'ocUserId', '3')"
        />
      </FormItem>
      <FormItem label="运控助理：" label-width="70px">
        <input
          v-model="formNameData.ocAssistantId"
          :placeholder="getDepartmentPermissions('3').canEdit ? '请选择' : ''"
          disabled
          @click="
            openPicker('运控助理', 'operationControl', 'ocAssistantId', '3')
          "
        />
      </FormItem>
      <FormItem label="值班经理：" label-width="70px">
        <input
          v-model="formNameData.dutyManagerId"
          :placeholder="getDepartmentPermissions('3').canEdit ? '请选择' : ''"
          disabled
          @click="
            openPicker('值班经理', 'operationControl', 'dutyManagerId', '3')
          "
        />
      </FormItem>
      <FormItem v-if="showRemarks" label="备注：" label-width="70px">
        <input
          v-model="formIdData.ocRemark"
          :placeholder="getDepartmentPermissions('3').canEdit ? '请输入' : ''"
          :disabled="!getDepartmentPermissions('3').canEdit"
        />
      </FormItem>
    </view>

    <view>
      <view
        class="section-title"
        :class="
          getDepartmentPermissions('4').canEdit ? '' : 'section-title-disabled'
        "
        >{{ getSectionTitle('市场部') }}
      </view>
      <FormItem label="售票：" label-width="70px">
        <input
          v-model="formNameData.conductorId"
          :placeholder="getDepartmentPermissions('4').canEdit ? '请选择' : ''"
          disabled
          @click="openPicker('售票', 'conductor', 'conductorId', '4')"
        />
      </FormItem>
      <FormItem
        v-if="showRemarks"
        label="备注："
        label-width="70px"
        :is-disabled="!getDepartmentPermissions('4').canEdit"
      >
        <input
          v-model="formIdData.marketRemark"
          :placeholder="getDepartmentPermissions('4').canEdit ? '请输入' : ''"
          :disabled="!getDepartmentPermissions('4').canEdit"
        />
      </FormItem>
    </view>

    <!-- 人员选择弹窗 -->
    <van-popup
      :show="pickerData.show"
      position="bottom"
      :lock-scroll="true"
      custom-style="max-height: 60vh;"
      :root-portal="true"
    >
      <view class="popup-header">
        <text class="cancel" @click="closePicker">取消</text>
        <text class="title">{{ pickerData.title }}</text>
        <text class="confirm" @click="confirmPicker">确认</text>
      </view>
      <view class="popup-content">
        <!-- 多选组件 -->
        <van-checkbox-group v-if="isMultiSelect" :value="formCheckedIdData">
          <van-cell-group>
            <van-cell
              v-for="(item, index) in pickerData.list"
              :key="index"
              :title="item.userName"
              clickable
              @click="onRowChange(item.userId)"
            >
              <van-checkbox
                slot="right-icon"
                shape="square"
                :name="item.userId"
                :value="item.userId"
              />
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>

        <!-- 单选组件 -->
        <van-radio-group v-else :value="formCheckedIdData" :max="1">
          <van-cell
            v-for="(item, index) in pickerData.list"
            :key="index"
            :title="item.userName"
            clickable
            @click="onRowChange(item.userId)"
          >
            <van-radio
              slot="right-icon"
              :name="item.userId"
              :value="item.userId"
            />
          </van-cell>
        </van-radio-group>
      </view>
    </van-popup>
  </view>
</template>

<script>
import FormItem from '../../pages/flightTask/compoents/FormItem.vue'
import { listUserByRole } from '../../api/flightTask'
import { SUCCESS_CODE } from '../../utils/constant'
import { getDepartmentPermissions } from '../../utils'

export default {
  name: 'CrewModify',
  components: { FormItem },
  props: {
    // 组件类型：'update' | 'receive'
    type: {
      type: String,
      default: 'update',
      validator: (value) => ['update', 'receive'].includes(value),
    },
    // 初始人员数据
    initialData: {
      type: Object,
      default: () => ({}),
    },

    // 支持多选的字段数组
    multiSelectFields: {
      type: Array,
      default: () => ['mechanicId'],
    },
  },
  data() {
    return {
      formCheckedIdData: '', // 选择id的数据
      // 下拉选择数据
      pickerData: {
        show: false,
        title: '',
        list: [],
        formKey: '',
      },
      formNameData: {}, // 表单名称数据展示用
      formIdData: {}, // 表单id数据
      staffObj: {}, // 人员选择数据
    }
  },
  computed: {
    // 判断当前字段是否支持多选
    isMultiSelect() {
      return this.multiSelectFields.includes(this.pickerData.formKey)
    },
    // 是否显示备注字段
    showRemarks() {
      return this.type === 'receive'
    },
  },
  mounted() {
    this.getPickerListData().then(() => {
      if (this.initialData && Object.keys(this.initialData).length > 0) {
        this.initializeData(this.initialData)
      }
    })
    // console.log(getDepartmentPermissions('1'))
    // console.log(getDepartmentPermissions('2'))
    // console.log(getDepartmentPermissions('3'))
    // console.log(getDepartmentPermissions('4'))
  },
  watch: {
    // 监听初始数据变化
    initialData: {
      handler(newVal) {
        if (
          newVal &&
          Object.keys(newVal).length > 0 &&
          Object.keys(this.staffObj).length > 0
        ) {
          this.initializeData(newVal)
        }
      },
      immediate: false,
      deep: true,
    },
    // 监听表单数据变化，向父组件发送事件
    formIdData: {
      handler(newVal) {
        this.$emit('data-change', {
          formIdData: newVal,
          formNameData: this.formNameData,
        })
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    getDepartmentPermissions,
    // 获取下拉人员数据
    async getPickerListData() {
      const { response: res } = await listUserByRole()
      if (res.code === SUCCESS_CODE) {
        const userMap = { ...res.data.userMap }
        for (let key in userMap) {
          if (!userMap[key]) return
          userMap[key] = res.data.userMap[key].map((item) => {
            return {
              ...item,
              userId: String(item.userId) || '',
            }
          })
        }
        this.staffObj = {
          ...userMap,
          pilotType1: [
            { userName: '机长', userId: '机长' },
            { userName: '实习机长', userId: '实习机长' },
            { userName: '教员', userId: '教员' },
          ],
          pilotType2: [
            { userName: '副驾驶', userId: '副驾驶' },
            { userName: '副驾驶A', userId: '副驾驶A' },
            { userName: '学员', userId: '学员' },
            { userName: '同乘', userId: '同乘' },
          ],
        }
      }
    },

    // 获取部门标题
    getSectionTitle(department) {
      if (this.type === 'receive') {
        return `${department}人员指派`
      }
      return department
    },

    // 获取副机长标签
    getSubLabel(label) {
      if (this.type === 'receive') {
        return '副驾驶：'
      }
      return `${label}：`
    },

    // 获取副驾驶选择器标题
    getSubPickerTitle(title) {
      if (this.type === 'receive') {
        return `${title}类型`
      }
      return `${title}类型`
    },

    // 初始化数据
    initializeData(data) {
      if (this.type === 'update') {
        // updateCrew页面的数据处理逻辑
        this.formNameData = {
          ...data,
          crewRole1: data.pilotType,
          crewRole2: data.copilotType,
          captainId: data.captain,
          copilotId: data.copilot,
          maintenanceId: data.maintenance,
          mechanicId: data.mechanic,
          inspectorId: data.inspector,
          organizationId: data.organization,
          ocUserId: data.ocUser,
          ocAssistantId: data.ocAssistant,
          dutyManagerId: data.dutyManager,
          conductorId: data.conductor,
        }

        this.formIdData = {
          ...data,
          captainId: this.transformPickerIdData(data.captain, 'pilot'),
          copilotId: this.transformPickerIdData(data.copilot, 'pilot'),
          conductorId: this.transformPickerIdData(data.conductor, 'conductor'),
          inspectorId: this.transformPickerIdData(
            data.inspector,
            'operationControl'
          ),
          mechanicId: this.transformPickerIdData(data.mechanic, 'maintenance'),
          maintenanceId: this.transformPickerIdData(
            data.maintenance,
            'maintenance'
          ),
          ocAssistantId: this.transformPickerIdData(
            data.ocAssistant,
            'operationControl'
          ),
          ocUserId: this.transformPickerIdData(data.ocUser, 'operationControl'),
          organizationId: this.transformPickerIdData(
            data.organization,
            'operationControl'
          ),
          dutyManagerId: this.transformPickerIdData(
            data.dutyManager,
            'operationControl'
          ),
        }
      } else {
        // receiveDetail页面的数据处理逻辑
        const crew = data.crew
        if (crew) {
          this.formNameData = {
            ...crew,
            captainId: this.transformPickerData(crew.captainId, 'pilot'),
            conductorId: this.transformPickerData(
              crew.conductorId,
              'conductor'
            ),
            copilotId: this.transformPickerData(crew.copilotId, 'pilot'),
            inspectorId: this.transformPickerData(
              crew.inspectorId,
              'operationControl'
            ),
            mechanicId: this.transformPickerData(
              crew.mechanicId,
              'maintenance'
            ),
            maintenanceId: this.transformPickerData(
              crew.maintenanceId,
              'maintenance'
            ),
            ocAssistantId: this.transformPickerData(
              crew.ocAssistantId,
              'operationControl'
            ),
            ocUserId: this.transformPickerData(
              crew.ocUserId,
              'operationControl'
            ),
            organizationId: this.transformPickerData(
              crew.organizationId,
              'operationControl'
            ),
            dutyManagerId: this.transformPickerData(
              crew.dutyManagerId,
              'operationControl'
            ),
          }
          this.formIdData = crew || {}
        }
      }
    },

    // 打开选择器
    openPicker(title, listKey, formKey, roleKey) {
      if (!getDepartmentPermissions(roleKey).canEdit) return
      this.pickerData = {
        show: true,
        title: title,
        list: this.staffObj[listKey],
        formKey: formKey,
      }

      // 根据是否支持多选初始化数据
      const currentValue = this.formIdData[formKey]
      const isMultiSelectField = this.multiSelectFields.includes(formKey)

      if (isMultiSelectField) {
        // 多选字段：初始化为数组
        if (currentValue) {
          this.formCheckedIdData =
            typeof currentValue === 'string'
              ? currentValue.split(',').filter(Boolean)
              : [currentValue]
        } else {
          this.formCheckedIdData = []
        }
      } else {
        // 单选字段：初始化为字符串
        this.formCheckedIdData = currentValue || ''
      }
    },

    // 关闭选择器
    closePicker() {
      this.pickerData = {
        show: false,
        title: '',
        list: [],
        formKey: '',
      }
      this.formCheckedIdData = []
    },

    // 根据选中的id展示名字
    transformPickerData(id, key) {
      if (id && key) {
        // 支持单个和多个人员的解析
        const ids =
          typeof id === 'string' ? id.split(',').filter(Boolean) : [id]
        const names = ids
          .map((singleId) => {
            const user =
              this.staffObj[key] &&
              this.staffObj[key].find((item) => item.userId == singleId)
            return user ? user.userName : ''
          })
          .filter(Boolean)
        return names.join(', ')
      }
      return ''
    },

    // 根据名字回填id
    transformPickerIdData(name, key) {
      if (name && key) {
        // 支持单个和多个人员的解析
        const names =
          typeof name === 'string' ? name.split(',').filter(Boolean) : [name]
        const ids = names
          .map((singleId) => {
            const user =
              this.staffObj[key] &&
              this.staffObj[key].find((item) => item.userName == singleId)
            return user ? user.userId : ''
          })
          .filter(Boolean)
        return ids.join(', ')
      }
      return ''
    },

    // 确认选择
    confirmPicker() {
      const isMultiSelectField = this.multiSelectFields.includes(
        this.pickerData.formKey
      )

      if (isMultiSelectField) {
        // 多选字段处理
        if (this.formCheckedIdData && this.formCheckedIdData.length > 0) {
          // 将数组转换为逗号分隔的字符串
          this.formIdData[this.pickerData.formKey] =
            this.formCheckedIdData.join(',')
          // 获取选中人员的名称
          const selectedNames = this.formCheckedIdData
            .map((id) => {
              const user = this.pickerData.list.find(
                (item) => item.userId == id
              )
              return user ? user.userName : ''
            })
            .filter(Boolean)
          this.formNameData[this.pickerData.formKey] = selectedNames.join(', ')
        }
      } else {
        // 单选字段处理
        if (this.formCheckedIdData) {
          this.formIdData[this.pickerData.formKey] = this.formCheckedIdData
          this.formNameData[this.pickerData.formKey] =
            this.pickerData.list.find(
              (item) => item.userId == this.formCheckedIdData
            )?.userName || ''
        }
      }
      this.$emit('data-change', {
        formIdData: this.formIdData,
        formNameData: this.formNameData,
      })
      this.closePicker()
    },

    // 行选择变化
    onRowChange(data) {
      const isMultiSelectField = this.multiSelectFields.includes(
        this.pickerData.formKey
      )

      if (isMultiSelectField) {
        // 多选
        const index = this.formCheckedIdData.indexOf(data)
        if (index > -1) {
          this.formCheckedIdData.splice(index, 1)
        } else {
          this.formCheckedIdData.push(data)
        }
      } else {
        // 单选
        this.formCheckedIdData = data
      }
    },

    // 获取表单数据（供父组件调用）
    getFormData() {
      return {
        formIdData: this.formIdData,
        formNameData: this.formNameData,
      }
    },
  },
}
</script>

<style scoped lang="scss">
.crew-modify {
  width: 100%;
}

.section-title {
  font-size: 14px;
  padding: 12px 0 0 8px;
  position: relative;
  color: #2c5de5;

  &::before {
    content: '';
    display: block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #2c5de5;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(50%);
  }
}

.section-title-disabled {
  color: #999;

  &::before {
    background: #999;
  }
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  box-sizing: border-box;

  .cancel {
    color: #999;
    font-size: 12px;
  }

  .confirm {
    color: #2c5de5;
    font-size: 12px;
  }

  .title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
  }
}

.popup-content {
  padding: 16px;
  box-sizing: border-box;
  max-height: 80%;
  overflow-y: auto;
}
</style>

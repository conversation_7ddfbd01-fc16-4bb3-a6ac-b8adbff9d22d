<template>
	<view class="warp" >
		<view class="">
			<image class='plane' src="../../static/images/plane_bg.png" mode="widthFix"></image>
				<view class="main">
					<view class="header flex-row fz-12">
						<span class="index">{{taskIndex + 1}}</span>
						<span class="marRight33 white-nowrap">
							<text class="iconfont icon-seedling-fill col-gray-400 margin-right-4"></text>
							<span class="col-gray-500 marRight4" style='font-size: 13px;'>任务性质</span>
							<span class="col-gray-600">{{taskInfo.flightPurpose}}</span>
						</span>
						<span class="white-nowrap">
							<text class="iconfont icon-refresh-fill col-gray-400 margin-right-4"></text>
							<span class="col-gray-500 marRight4" style='font-size: 13px;'>{{taskInfo.flightDate}}</span>
							<!-- <span class="col-gray-600">{{taskInfo.taskNumber}}</span> -->
						</span>
					</view>
					<!-- <view class="trip-address flex-row">
						<image class='trip-bar' src="../../static/images/tripBar.png"></image>
						<view class="flex-col justify-between">
							<view>
								<p class="fz-16 col-gray-700">{{taskInfo.departCity}}</p>
								<p class="fz-12 col-gray-500">{{taskInfo.flightDate +'  '+ taskInfo.planDepartTime}}</p>
							</view>
							<view>
								<p class="fz-16 col-gray-700">{{taskInfo.arriveCity}}</p>
								<p class="fz-12 col-gray-500">{{taskInfo.planArriveTime}}</p>
							</view>
						</view>
					</view> -->
				</view>

				<view class="detail-row bg-white-500 no-fold">
						<view class="detail-block">
							<p class="fz-12 col-gray-500">出发</p>
							<p class="fz-14 col-gray-600">{{taskInfo.departCity}}</p>
						</view>
						<view class="detail-block">
							<p class="fz-12 col-gray-500">到达</p>
							<p  class="fz-14 col-gray-600">{{taskInfo.arriveCity}}</p>
						</view>
						<view class="detail-block">
							<p class="fz-12 col-gray-500">备降</p>
							<p  class="fz-14 col-gray-600">{{taskInfo.alternateCity}}</p>
						</view>
				</view>
				<view class="detail-row bg-white-500 no-fold">
						<view class="detail-block">
							<p class="fz-12 col-gray-500">出发三字码</p>
							<p class="fz-14 col-gray-600">{{taskInfo.departAirportCode}}</p>
						</view>
						<view class="detail-block">
							<p class="fz-12 col-gray-500">到达三字码</p>
							<p  class="fz-14 col-gray-600">{{taskInfo.arriveAirportCode}}</p>
						</view>
						<view class="detail-block">
							<p class="fz-12 col-gray-500">备降三字码</p>
							<p  class="fz-14 col-gray-600">{{taskInfo.alternateAirportCode}}</p>
						</view>
				</view>
				<view  class="detail-row bg-white-500 no-fold">
						<!-- <view class="detail-block">
							<p class="fz-12 col-gray-500">运控人员</p>
							<p class="fz-14 col-gray-600">{{taskInfo.ocUser}}</p>
						</view> -->
						<!-- <view class="detail-block">
							<p class="fz-12 col-gray-500">值班领导</p>
							<p  class="fz-14 col-gray-600">{{taskInfo.leader}}</p>
						</view>
						<view class="detail-block">
							<p class="fz-12 col-gray-500">值班总领导</p>
							<p  class="fz-14 col-gray-600">{{taskInfo.generalleader}}</p>
						</view> -->
						<view class="detail-block">
							<p class="fz-12 col-gray-500">呼号</p>
							<p class="fz-14 col-gray-600">{{taskInfo.callSign}}</p>
						</view>
						<!-- <view class="detail-block">
							<p class="fz-12 col-gray-500">运行种类</p>
							<p  class="fz-14 col-gray-600">{{taskInfo.dperationType}}</p>
						</view> -->
						<view class="detail-block">
							<p class="fz-12 col-gray-500">运行标准</p>
							<p  class="fz-14 col-gray-600">{{taskInfo.dperationStandard}}</p>
						</view>
						<view class="detail-block" v-if="taskInfo.flightPurpose=='短途运输'">
							<p class="fz-12 col-gray-500">航班号</p>
							<p  class="fz-14 col-gray-600">{{taskInfo.flightNo}}</p>
						</view>

				</view>

				<!-- 班次 -->
				<view class="">
					<view  :class="showMore ? 'show-view':'close-view'" class="bg-white-500 fold detail-row no-fold">
							<view class="detail-block">
								<p class="fz-12 col-gray-500">机长</p>
								<p class="fz-14 col-gray-600">{{taskInfo.captain}}</p>
							</view>
							<view class="detail-block">
								<p class="fz-12 col-gray-500">副驾驶</p>
								<p  class="fz-14 col-gray-600">{{taskInfo.copilot}}</p>
							</view>
							<view class="detail-block">
								<p class="fz-12 col-gray-500">放行员</p>
								<p  class="fz-14 col-gray-600">{{taskInfo.maintenance}}</p>
							</view>
					</view>
					<view  :class="showMore ? 'show-view':'close-view'" class="bg-white-500 fold detail-row">

							<view class="detail-block">
								<p class="fz-12 col-gray-500">责任运控</p>
								<p  class="fz-14 col-gray-600">{{taskInfo.ocUser}}</p>
							</view>
							<view class="detail-block">
								<p class="fz-12 col-gray-500">保障员</p>
								<p  class="fz-14 col-gray-600">{{taskInfo.safetyOfficer}}</p>
							</view>

					</view>

				</view>

				<!-- 	<view :class="showMore ? 'show-view':'close-view'" class="bg-white-500 fold detail-row">
						<view class="detail-block">
							<p class="fz-12 col-gray-500">放行员直接准备</p>
							<p  class="fz-14 col-gray-600">查看</p>
						</view>
						<view class="detail-block">
							<p class="fz-12 col-gray-500">运控直接准备</p>
							<p  class="fz-14 col-gray-600">查看</p>
						</view>
				</view> -->
				<view class="arrow bg-white-500" @click.stop="changeShow">
					<text v-show="showMore" class="iconfont icon-arrow-up-line"></text>
					<text v-show="!showMore" class="iconfont icon-arrow-down-line"></text>
				</view>

			</view>
		</view>
	</view>
</template>

<script>
	import { flightStatus } from '../../api/weChat.js';
	export default {
		name:"TaskCardstor",
	    props: {
		  taskInfo:{
			  type:Object,
		  },
		  taskIndex:{
			  type:Number
		  },
		  types:{
			  type:String
		  },
		  flight:{
			  type:String
		  }
	    },
		created() {
			this.userRole = uni.getStorageSync('userRole');
			this.flagDD = false
			this.userRole.map((i)=>{
				if(i == 'pilot'){
					this.flagDD = true
				}
			})
		},
		mounted() {
			console.log(111111111,this.taskInfo)
		},
		data() {
			return {
				userRole:"",
				showMore:false,
				flagDD:false
			}
		},
		methods: {
			async flightStatusClick(type,id){
				console.log(type,id)
				let params = {}
				// return false
				if(type == 2){
					params = {
						flightSortiesId: id,
						flightSortiesStatus: 3
					}

				}else{
					params = {
						flightSortiesId: id,
						flyStatus: 2
					}

				}

				uni.showModal({
					title: '请确认操作！',
					content: '',
					success: async function(resp) {
						if (resp.confirm) {
							try {
								const res = await flightStatus(params);
								uni.showToast({
									title: res.response.msg
								})
							} catch (e) {
								console.error(e)
							}
						} else if (resp.cancel) {
							console.log('用户点击取消');
						}
					}
				});


			},
			check(type,flightsortiesId){

				if(type==0){
					uni.navigateTo({
						url: '/pages/todo/advanceCheck?flightSortiesId='+flightsortiesId+'&type=0'
					});
				}if(type==1){
					uni.navigateTo({
						url: '/pages/todo/advanceCheck?flightSortiesId='+flightsortiesId+'&type=1'
					});
				}else if(type==2){
					uni.navigateTo({
						url: '/pages/todo/advanceCheck?flightSortiesId='+flightsortiesId+'&type=2'
					});
				}else if(type==3){
					uni.navigateTo({
						url: '/pages/flightPlan/message?flightSortiesId='+flightsortiesId
					});
				}else if(type==4){
					// uni.navigateTo({
					// 	url: '/pages/todo/advanceAprrol?flightplanId='+this.taskInfo.flightplan.flightplanId+'&type=222'
					// });
				}else if(type==5){
					uni.navigateTo({
						url: '/pages/todo/captionDerectCheck?flightSortiesId='+flightsortiesId
					});
				}else if(type==6){
					uni.navigateTo({
						url: '/pages/todo/copolitDerectCheck?flightSortiesId='+flightsortiesId
					});
				}else if(type==7){
					uni.navigateTo({
						url: '/pages/todo/maintenanceDerectCheck?flightSortiesId='+flightsortiesId
					});
				}else if(type==8){
					uni.navigateTo({
						url: '/pages/todo/safemaintenanceDerectCheck?flightSortiesId='+flightsortiesId
					});
				}else if(type==9){
					uni.navigateTo({
						url: '/pages/todo/givenFlight?flightSortiesId='+flightsortiesId+'&flightplanId='+this.taskInfo.flightplan.flightplanId
					});
				}else if(type==10){
					uni.navigateTo({
						url: '/pages/flightPlan/detail?flightSortiesId='+flightsortiesId
					});
				}
			},
			changeShow(){
				this.showMore = !this.showMore;
			},
			goflightDetail(){
				if(this.types==1){
					return false
				}
				console.log('/pages/flightPlan/flight?flightplanId=' + this.taskInfo.flightplan.flightplanId)
				uni.navigateTo({
				    url: '/pages/flightPlan/flight?flightplanId=' + this.taskInfo.flightplan.flightplanId
				});

			},
			changeShow(){
				this.showMore = !this.showMore;
			},
			goFlyTest(){ //飞前问答
				if(this.taskInfo.flyQaStatus === 0){
					uni.navigateTo({
					    url: '/pages/home/<USER>' + this.taskInfo.flightSortiesId
					});
				}
			},
			phoneCall(phoneNumber){
				uni.makePhoneCall({
				      phoneNumber: phoneNumber,
				    })
			},
			goAirLineData(){ //航线资料
				if(this.taskInfo.routeInfoStatus === 1){
					uni.navigateTo({
					    url: '/pages/home/<USER>' + this.taskInfo.flightSortiesId
					});
				}
			},
			goMeteReview(){ //气象资料
				if(this.taskInfo.meteReviewStatus !== 0){
					uni.navigateTo({
					    url: '/pages/home/<USER>' + this.taskInfo.flightSortiesId
					});
				}
			},
			goEleManifest(){ //电子舱单
				if(this.taskInfo.eleManifestStatus !== 0){
					uni.navigateTo({
					    url: '/pages/home/<USER>' + this.taskInfo.flightSortiesId
					});
				}
			},
			goReleaseSlip(){ //放行单
				if(this.taskInfo.releasenoteStatus !== 0){
					uni.navigateTo({
					    url: '/pages/home/<USER>' + this.taskInfo.flightSortiesId
					});
				}
			},
			goFlyTime(){ //飞行时刻
				uni.navigateTo({
					url: '/pages/home/<USER>' + this.taskInfo.flightSortiesId
				});
			}
		}
	}
</script>

<style lang="less" scoped>
	.warp{
		position: relative;
		overflow: hidden;
		background-color: rgba(255,255,255,0.64);
		border: 1px solid rgba(255,255,255,0.72);
		border-radius: 8px;
		margin-bottom: 12px;
	}
	.plane {
		width: 190px;
		position: absolute;
		top:0;right:0;
	}
	.main{
		padding: 24rpx 32rpx 0 32rpx;
		height: 45px;
		background-color: rgba(0,0,0,0);
		.header{
			padding-right: 84rpx;
			height: 16px;
			.index{
				width: 16px;
				height: 16px;
				line-height: 16px;
				text-align: center;
				margin-right: 13px;
				border-radius: 50%;
				color: #838791;
				background-color: #EAEAEB;
				display: inline-block;
			}
			.marRight4{
				margin-right: 8rpx;
			}
			.marRight33{
				margin-right: 66rpx;
			}
		}
		.trip-address{
			margin-top: 16px;
			margin-bottom: 12px;
			.fz-16{
				font-weight: 800;
			}
		}
		.trip-bar{
			width: 16px;
			height: 104px;
			margin-right: 12px;
		}

		.show-view{
				height: 330px;
				transition: .5s;
				backdrop-filter: blur(16px);
			}
			.close-view{
				height: 0;
				transition: .5s;
				overflow: hidden;
			}
	}
	.detail-row{
		padding: 12px 16px;
		display: flex;
		width: 100%;
		backdrop-filter: blur(16px);
		justify-content: space-between;
	}

	.show-view{
		height: 50px;
		transition: .5s;
		backdrop-filter: blur(16px);
	}
	.close-view{
		height: 0;
		transition: .5s;
		overflow: hidden;
	}
	.detail-block{
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: top;
		align-items: center;
		position: relative;
		.phone{
			position: absolute;
			bottom: -17px;
			font-size: 12px;
			color: #2C5DE5;
		}
		.icon-phone-fill{
			font-size: 12px!important;
		}
		.fz-14{
			font-weight: 600;
		}
	}
	//机型机号等样式调整
	.no-fold {
		padding-bottom: 12px;
	}
	.fold {
		padding: 0 16px;

		.detail-row {
			padding: 0;

			&:not(:last-child) {
				margin-bottom: 12px;
			}

			&:last-child {
				padding-top: 12px;
			}

			.fz-14 {
				margin-top: 4px;
			}

			&:nth-of-type(2) {
				.fz-14 {
					margin-bottom: 4px;
				}
			}
		}
	}

	.handel-section{
		width: 100%;
		padding:12px 0;
		display: flex;
		flex-wrap: wrap;
		.handle-block{
			padding: 8px 10.5px;
			width: calc(50% - 8px);
			height: 60px;
			border: 1px solid #D9DADD;
			border-radius: 4px;
			display: flex;
			margin-bottom: 12px;
		}
		.handle-block:nth-child(odd){
			margin-right: 16px;
		}
		.status{
			height: 20px;
			line-height: 20px;
			text-align: center;
			border-radius: 50px;
			width: fit-content;
			padding: 0 8px;
		}
		.complete{  //已完成的状态
			background: #E9FBF1;
			color: #5BBC72;
			border: 1px solid #D6F3E2;
		}
		.incomplete{  //未完成的状态
			background: #F6F6F6;
			color: #50545E;
			border: 1px solid #D9DADD;
		}
		.refuse{ //已拒绝状态
			background: #FFEFF0;
			color: #E83F4E;
			border: 1px solid #FFD6D7;
		}
	}
	.arrow{
		height: 20px;
		text-align: center;
		backdrop-filter: blur(16px);
	}

	.show-view.stories{
		height: 30px;
		font-weight: 600;
		border-top: 1px solid #efefef;
	}
	.show-view.titles{
		height: 50px !important;
		line-height: 40px !important;
	}
</style>

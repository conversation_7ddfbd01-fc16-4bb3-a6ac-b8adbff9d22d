<template>
  <view class="warp">
    <view class="">
      <image
        class="plane"
        src="../../static/images/plane_bg.png"
        mode="widthFix"
      ></image>
      <view class="main">
        <view class="header flex-row fz-12">
          <span class="index">{{ taskIndex + 1 }}</span>
          <span class="col-gray-500 white-nowrap marRight6">{{
            taskInfo.flightType == 1 ? '航线' : '空域'
          }}</span>
          <span class="marRight33 white-nowrap">
            <!--            <text-->
            <!--              class="iconfont icon-seedling-fill col-gray-400 margin-right-4"-->
            <!--            ></text>-->
            <!--            <span class="col-gray-500 marRight4" style="font-size: 13px"-->
            <!--              >任务性质</span-->
            <!--            >-->
            <span class="col-gray-600">{{ taskInfo.flightPurpose }}</span>
          </span>
          <span class="white-nowrap">
            <text
              class="iconfont icon-refresh-fill col-gray-400 margin-right-4"
            ></text>
            <span class="col-gray-500 marRight4">{{
              taskInfo.flightDate
            }}</span>
          </span>
          <span class="white-nowrap">
            <text
              v-if="taskInfo.flightStatus === 1"
              class="status-dot status-dot-green"
              >计划中</text
            >
            <text
              v-else-if="taskInfo.flightStatus === 2"
              class="status-dot status-dot-yellow"
              >已执行</text
            >
            <text
              v-else-if="taskInfo.flightStatus === 3"
              class="status-dot status-dot-red"
              >已取消</text
            >
          </span>
        </view>
      </view>

      <view class="detail-row bg-white-500 no-fold" style="padding-top: 0">
        <view class="detail-block">
          <p class="fz-12 col-gray-500">出发</p>
          <p class="fz-14 col-gray-600">{{ taskInfo.departCity }}</p>
        </view>
        <view class="detail-block">
          <p class="fz-12 col-gray-500">到达</p>
          <p class="fz-14 col-gray-600">{{ taskInfo.arriveCity }}</p>
        </view>
        <view class="detail-block">
          <p class="fz-12 col-gray-500">机尾号</p>
          <p class="fz-14 col-gray-600">{{ taskInfo.aircraftTailNo }}</p>
        </view>
      </view>
      <view class="detail-row bg-white-500 no-fold">
        <view class="detail-block">
          <p class="fz-12 col-gray-500">起飞时间</p>
          <p class="fz-14 col-gray-600">{{ taskInfo.planDepartTime }}</p>
        </view>
        <view class="detail-block">
          <p class="fz-12 col-gray-500">降落时间</p>
          <p class="fz-14 col-gray-600">{{ taskInfo.planArriveTime }}</p>
        </view>
      </view>
      <!-- 人员 -->
      <view class="">
        <!--飞行部        -->
        <view class="bg-white-500 detail-row no-fold">
          <view
            class="detail-block"
            :class="isCurrentUser(formNameData.captainId) && 'current-user'"
          >
            <p class="fz-12 col-gray-500">
              {{ taskInfo.pilotType || '机长' }}
            </p>
            <p class="fz-14 col-gray-600">{{ formNameData.captainId }}</p>
          </view>
          <view
            class="detail-block"
            :class="isCurrentUser(formNameData.copilotId) && 'current-user'"
          >
            <p class="fz-12 col-gray-500">
              {{ taskInfo.copilotType || '副驾驶' }}
            </p>
            <p class="fz-14 col-gray-600">{{ formNameData.copilotId }}</p>
          </view>
        </view>
        <!--        机务部-->
        <view class="bg-white-500 detail-row no-fold">
          <view
            class="detail-block"
            :class="isCurrentUser(formNameData.maintenanceId) && 'current-user'"
          >
            <p class="fz-12 col-gray-500">放行</p>
            <p class="fz-14 col-gray-600">
              {{ formNameData.maintenanceId }}
            </p>
          </view>
          <view
            class="detail-block"
            :class="isCurrentUser(formNameData.mechanicId) && 'current-user'"
          >
            <p class="fz-12 col-gray-500">机械员</p>
            <p class="fz-14 col-gray-600">
              {{ formNameData.mechanicId }}
            </p>
          </view>
        </view>
        <!--       运控中心 -->
        <view class="bg-white-500 detail-row no-fold">
          <view
            class="detail-block"
            :class="isCurrentUser(formNameData.inspectorId) && 'current-user'"
          >
            <p class="fz-12 col-gray-500">安检员</p>
            <p class="fz-14 col-gray-600">{{ formNameData.inspectorId }}</p>
          </view>
          <view
            class="detail-block"
            :class="
              isCurrentUser(formNameData.organizationId) && 'current-user'
            "
          >
            <p class="fz-12 col-gray-500">现场组织</p>
            <p class="fz-14 col-gray-600">{{ formNameData.organizationId }}</p>
          </view>
          <view
            class="detail-block"
            :class="isCurrentUser(formNameData.ocUserId) && 'current-user'"
          >
            <p class="fz-12 col-gray-500">责任运控</p>
            <p class="fz-14 col-gray-600">{{ formNameData.ocUserId }}</p>
          </view>
        </view>
        <view class="bg-white-500 detail-row no-fold">
          <view
            class="detail-block"
            :class="isCurrentUser(formNameData.ocAssistantId) && 'current-user'"
          >
            <p class="fz-12 col-gray-500">运控助理</p>
            <p class="fz-14 col-gray-600">{{ formNameData.ocAssistantId }}</p>
          </view>
          <view
            class="detail-block"
            :class="isCurrentUser(formNameData.dutyManagerId) && 'current-user'"
          >
            <p class="fz-12 col-gray-500">值班经理</p>
            <p class="fz-14 col-gray-600">{{ formNameData.dutyManagerId }}</p>
          </view>
          <!--市场部          -->
          <view
            class="detail-block"
            :class="isCurrentUser(formNameData.conductorId) && 'current-user'"
          >
            <p class="fz-12 col-gray-500">售票员</p>
            <p class="fz-14 col-gray-600">{{ formNameData.conductorId }}</p>
          </view>
        </view>
        <view
          class="bg-white-500 fold detail-row remark-row"
          :class="showMore ? 'show-view' : 'close-view'"
        >
          <view class="detail-block">
            <p class="fz-12 col-gray-500">飞行部备注:</p>
            <p class="fz-14 col-gray-600">{{ taskInfo.flightRemark }}</p>
          </view>
        </view>
        <view
          :class="showMore ? 'show-view' : 'close-view'"
          class="bg-white-500 fold detail-row remark-row"
        >
          <view class="detail-block">
            <p class="fz-12 col-gray-500">机务部备注:</p>
            <p class="fz-14 col-gray-600">{{ taskInfo.maintenanceRemark }}</p>
          </view>
        </view>
        <view
          :class="showMore ? 'show-view' : 'close-view'"
          class="bg-white-500 fold detail-row remark-row"
        >
          <view class="detail-block">
            <p class="fz-12 col-gray-500">运控中心备注:</p>
            <p class="fz-14 col-gray-600">
              {{ taskInfo.ocRemark }}
            </p>
          </view>
        </view>
        <view
          :class="showMore ? 'show-view' : 'close-view'"
          class="bg-white-500 fold detail-row remark-row"
        >
          <view class="detail-block">
            <p class="fz-12 col-gray-500">市场部备注:</p>
            <p class="fz-14 col-gray-600">{{ taskInfo.marketRemark }}</p>
          </view>
        </view>
        <view
          v-if="showFlag"
          style="
            overflow: hidden;
            background: #fff;
            padding-bottom: 20px;
            padding-left: 25px;
            font-weight: 800;
          "
        >
          <view class="leftBox" style="float: left">选择取消原因：</view>
          <view class="rightBox" style="float: left">
            <view class="uni-list-cell">
              <picker
                mode="selector"
                :value="index"
                @change="bindPickerChange"
                :range="arraysas"
              >
                {{ arraysas[index] }}
              </picker>
            </view>
          </view>
        </view>
        <view
          :class="showMore ? 'show-view' : 'close-view'"
          class="bg-white-500 fold detail-row"
          v-if="showOption"
        >
          <!--          <button-->
          <!--            style="height: 30px"-->
          <!--            v-if="taskInfo.identity.includes('captain')"-->
          <!--            type="primary"-->
          <!--            size="mini"-->
          <!--            @click="flightStatusClick(1, taskInfo.flightplanId)"-->
          <!--          >-->
          <!--            飞行完成-->
          <!--          </button>-->
          <button
            style="height: 30px"
            v-if="taskInfo.flightStatus == 1"
            type="warn"
            size="mini"
            @click="flightStatusClick(2, taskInfo.flightplanId)"
          >
            飞行取消
          </button>
          <button
            v-if="taskInfo.flightStatus === 1"
            style="height: 30px"
            size="mini"
            @click="submitCrew(taskInfo.flightplanId)"
          >
            修改人员
          </button>
        </view>
      </view>

      <view class="arrow bg-white-500" @click.stop="changeShow">
        <text v-show="showMore" class="iconfont icon-arrow-up-line"></text>
        <text v-show="!showMore" class="iconfont icon-arrow-down-line"></text>
      </view>
    </view>
  </view>
  <!--  </view>-->
</template>

<script>
import { getFlightflyEnd, getFlightcancelFlight } from '../../api/weChat.js'

export default {
  name: 'TaskCard',
  props: {
    taskInfo: {
      type: Object,
    },
    taskIndex: {
      type: Number,
    },
    types: {
      type: String,
    },
    flight: {
      type: String,
    },
    flightDate: {
      type: String,
    },
    showOption: {
      type: Boolean,
      default: true,
    },
  },
  created() {
    this.userRole = uni.getStorageSync('userRole')
    if (this.userRole) {
      this.userRole.includes('pilot')
    }
    this.userInfo = uni.getStorageSync('userInfo')
  },
  data() {
    return {
      userRole: '',
      showMore: false,
      arraysas: [
        '天气原因',
        '空管原因',
        '军事活动',
        '飞机故障',
        '航班计划调整',
        '航务保障',
        '机组原因',
        '机场原因',
        '油料原因',
        '旅客原因',
      ],
      value: '',
      index: 0,
      showFlag: false,

      formNameData: {}, //表单名称数据展示用
      roleType: '', //角色类型
      userInfo: '',
    }
  },
  watch: {
    taskInfo: {
      immediate: true,
      handler(newVal) {
        this.getUserName(newVal)
      },
    },
  },
  methods: {
    bindPickerChange(e) {
      this.index = e.detail.value
    },
    flightStatusClick(type, id) {
      let that = this
      let params = {}
      // return false
      if (type == 2) {
        console.log(this.showFlag)
        if (this.showFlag) {
          uni.showModal({
            title: '请确认操作！',
            content: '',
            success: async function (resp) {
              if (resp.confirm) {
                params = {
                  flightPlanId: id,
                  flightStatus: 3,
                  flightCancelReason: that.arraysas[that.index],
                }
                try {
                  const res = await getFlightcancelFlight(params)
                  uni.showToast({
                    title: res.response.msg,
                  })
                  setTimeout(() => {
                    this.$emit('listenToChildEvent')
                  }, 1000)
                  // setTimeout(function(){
                  // 	that.$emit('listenToChildEvent',123)
                  // },2000)
                } catch (e) {
                  console.error(e)
                }
              } else if (resp.cancel) {
                console.log('用户点击取消')
              }
            },
          })
        }
        this.showFlag = true
      } else {
        params = {
          flightPlanId: id,
          flyStatus: 3,
        }
        uni.showModal({
          title: '请确认操作！',
          content: '',
          success: async function (resp) {
            if (resp.confirm) {
              try {
                const res = await getFlightflyEnd(params)
                uni.showToast({
                  title: res.response.msg,
                })
              } catch (e) {
                console.error(e)
              }
            } else if (resp.cancel) {
              console.log('用户点击取消')
            }
          },
        })
      }
    },
    changeShow() {
      this.showMore = !this.showMore
    },
    //处理人员数据回显示
    getUserName(newVal) {
      this.formNameData = {
        captainId: newVal.captain,
        copilotId: newVal.copilot,
        maintenanceId: newVal.maintenance,
        mechanicId: newVal.mechanic,
        inspectorId: newVal.inspector,
        organizationId: newVal.organization,
        ocUserId: newVal.ocUser,
        ocAssistantId: newVal.ocAssistant,
        dutyManagerId: newVal.dutyManager,
        conductorId: newVal.conductor,
      }
    },
    //人员修改
    async submitCrew(id) {
      uni.navigateTo({
        url: `/pages/flightPlan/updateCrew?taskId=${id}`,
      })
    },
    //是否当前登录用户
    isCurrentUser(userName) {
      return String(userName) === String(this.userInfo.userName)
    },
  },
}
</script>

<style lang="less" scoped>
.warp {
  position: relative;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.64);
  border: 1px solid rgba(255, 255, 255, 0.72);
  border-radius: 8px;
  margin-bottom: 12px;
}

.plane {
  width: 190px;
  position: absolute;
  top: 0;
  right: 0;
}

.main {
  padding: 24rpx 32rpx 0 32rpx;
  height: 45px;
  background-color: rgba(0, 0, 0, 0);

  .header {
    display: flex;
    align-items: center;
    //padding-right: 84rpx;
    height: 16px;
    justify-content: space-between;

    .index {
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      margin-right: 13px;
      border-radius: 50%;
      color: #838791;
      background-color: #eaeaeb;
      display: inline-block;
    }

    .marRight4 {
      margin-right: 8rpx;
    }

    .marRight6 {
      margin-right: 12rpx;
    }

    .marRight33 {
      margin-right: 66rpx;
    }
  }

  .trip-address {
    margin-top: 16px;
    margin-bottom: 12px;

    .fz-16 {
      font-weight: 800;
    }
  }

  .trip-bar {
    width: 16px;
    height: 104px;
    margin-right: 12px;
  }

  .show-view {
    height: 330px;
    transition: 0.5s;
    backdrop-filter: blur(16px);
  }

  .close-view {
    height: 0;
    transition: 0.5s;
    overflow: hidden;
  }
}

.detail-row {
  padding: 0 16px;
  display: flex;
  width: 100%;
  backdrop-filter: blur(16px);
  //justify-content: space-between;

  .detail-block {
    width: 33.3%;
    align-items: flex-start;
  }
}

.remark-row {
  justify-content: flex-start;

  .detail-block {
    padding-bottom: 10px;
    width: 100%;
    flex-direction: row;
    justify-content: flex-start;
    gap: 10px;
  }
}

.current-user {
  view {
    color: #00b050;
  }
}

.show-view {
  //min-height: 30px;
  //height: 50px;
  transition: 0.5s;
  backdrop-filter: blur(16px);
}

.close-view {
  height: 0;
  transition: 0.5s;
  overflow: hidden;
}

.detail-block {
  //flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: relative;

  .phone {
    position: absolute;
    bottom: -17px;
    font-size: 12px;
    color: #2c5de5;
  }

  .icon-phone-fill {
    font-size: 12px !important;
  }

  .fz-14 {
    font-weight: 600;
  }
}

//机型机号等样式调整
.no-fold {
  padding-bottom: 6px;
}

.fold {
  padding: 0 16px;

  .detail-row {
    padding: 0;

    &:not(:last-child) {
      margin-bottom: 12px;
    }

    &:last-child {
      padding-top: 12px;
    }

    .fz-14 {
      margin-top: 4px;
    }

    &:nth-of-type(2) {
      .fz-14 {
        margin-bottom: 4px;
      }
    }
  }
}

.handel-section {
  width: 100%;
  padding: 12px 0;
  display: flex;
  flex-wrap: wrap;

  .handle-block {
    padding: 8px 10.5px;
    width: calc(50% - 8px);
    height: 60px;
    border: 1px solid #d9dadd;
    border-radius: 4px;
    display: flex;
    margin-bottom: 12px;
  }

  .handle-block:nth-child(odd) {
    margin-right: 16px;
  }

  .status {
    height: 20px;
    line-height: 20px;
    text-align: center;
    border-radius: 50px;
    width: fit-content;
    padding: 0 8px;
  }

  .complete {
    //已完成的状态
    background: #e9fbf1;
    color: #00b050;
    border: 1px solid #d6f3e2;
  }

  .incomplete {
    //未完成的状态
    background: #f6f6f6;
    color: #50545e;
    border: 1px solid #d9dadd;
  }

  .refuse {
    //已拒绝状态
    background: #ffeff0;
    color: #e83f4e;
    border: 1px solid #ffd6d7;
  }
}

.arrow {
  height: 20px;
  text-align: center;
  backdrop-filter: blur(16px);
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  box-sizing: border-box;

  .cancel {
    color: #999;
    font-size: 12px;
  }

  .confirm {
    color: #2c5de5;
    font-size: 12px;
  }

  .title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
  }
}

.status-dot {
  position: relative;
  font-size: 12px;

  &::after {
    content: ' ';
    display: block;
    position: absolute;
    top: 4px;
    right: -12px;
    border-radius: 50%;
    width: 8px;
    height: 8px;
  }

  &-green {
    color: #00b050;

    &::after {
      background: #00b050;
    }
  }

  &-yellow {
    color: #ff822c;

    &::after {
      background: #ff822c;
    }
  }

  &-red {
    color: #ff0000;

    &::after {
      background: #ff0000;
    }
  }
}
</style>

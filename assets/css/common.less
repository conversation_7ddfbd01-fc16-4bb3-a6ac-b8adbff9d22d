@import "./variable.less";

pre {
  margin: 0;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-row-reverse {
  display: flex;
  flex-direction: row-reverse;
}

.flex-col-reverse {
  display: flex;
  flex-direction: column-reverse;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.flex-1 {
  flex: 1 0 0;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-col-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

//间隔
.gap-4 {
  gap: 4px;
}

.gap-8 {
  gap: 8px;
}

.gap-12 {
  gap: 12px;
}


// 定位
.p-fixed {
  position: fixed;
}

.dropdown-top {
  z-index: 998;
}

.p-absolute {
  position: absolute;
}

.p-relative {
  position: relative;
}

// 字体大小
.fz-12 {
  font-size: 12px;
}

.fz-14 {
  font-size: 14px;
}

.fz-16 {
  font-size: 16px;
}

.fz-18 {
  font-size: 18px;
}

.fz-20 {
  font-size: 20px;
}

.fz-24 {
  font-size: 24px;
}

.fz-28 {
  font-size: 28px;
}

.fw-normal {
  font-weight: 400;
}

.fw-bold {
  font-weight: 800;
}

.line-h-24 {
  height: 24px;
  line-height: 24px;
}

.line-h-22 {
  height: 22px;
  line-height: 22px;
}

.line-h-20 {
  height: 20px;
  line-height: 20px;
}

.cursor-p {
  cursor: pointer;
}

// 圆角边框
.b-r-4 {
  border-radius: 4px;
}

.b-r-8 {
  border-radius: 8px;
}

.w-100-p {
  width: 100%;
}

.width-60 {
  width: 60px;
}

.width-260 {
  width: 260px;
}

// 超出两行省略
.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}


// padding、margin
.m-b-20 {
  margin-bottom: 20px;
}

.m-l-12 {
  margin-left: 12px;
}

.m-l-4 {
  margin-left: 4px;
}

.white-nowrap {
  white-space: nowrap;
}

.text-center {
  text-align: center;
}

// 外边距
.margin-top-2 {
  margin-top: 2px;
}

.margin-top-8 {
  margin-top: 8px;
}

.margin-top-12 {
  margin-top: 12px;
}

.margin-top-20 {
  margin-top: 20px;
}

.margin-right-4 {
  margin-right: 4px;
}

.margin-right-8 {
  margin-right: 8px;
}

.margin-right-10 {
  margin-right: 10px;
}

.margin-right-16 {
  margin-right: 16px;
}

.margin-right-12 {
  margin-right: 12px;
}

.margin-right-18 {
  margin-right: 18px;
}

.margin-right-20 {
  margin-right: 20px;
}

.margin-right-33 {
  margin-right: 33px;
}

.margin-right-100 {
  margin-right: 100px;
}

.margin-bottom-2 {
  margin-bottom: 2px;
}

.margin-bottom-4 {
  margin-bottom: 4px;
}

.margin-bottom-8 {
  margin-bottom: 8px;
}

.margin-bottom-12 {
  margin-bottom: 12px;
}

.margin-bottom-16 {
  margin-bottom: 16px;
}

.margin-bottom-20 {
  margin-bottom: 20px;
}

.margin-bottom-24 {
  margin-bottom: 24px;
}

.margin-left-8 {
  margin-left: 8px;
}

.margin-left-16 {
  margin-left: 16px;
}

.margin-left-20 {
  margin-left: 20px;
}

.margin-left-50 {
  margin-left: 50px;
}

.margin-left-40 {
  margin-left: 40px;
}

.margin-left-right-8 {
  margin: 0px 8px;
}

.margin-top-bottom-10 {
  margin: 10px 0px;
}

// 内边距
.padding-left-10 {
  padding-left: 10px;
}

.padding-left-26 {
  padding-left: 26px;
}

.padding-right-10 {
  padding-right: 10px;
}

.padding-left-16 {
  padding-left: 16px;
}

.padding-left-18 {
  padding-left: 18px;
}

.padding-right-16 {
  padding-right: 16px;
}

.padding-right-18 {
  padding-right: 18px;
}

.padding-tb-8 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.padding-tb-10 {
  padding-left: 18px;
  padding-right: 18px;
}

.padding-lr-20 {
  padding: 0 20px;
}

.m-t-30 {
  margin-top: 30px;
}

// 高
.height-20 {
  height: 20px;
}

.height-22 {
  height: 22px;
}

.height-24 {
  height: 24px;
}

.height-28 {
  height: 28px;
}

.height-40 {
  height: 40px;
}

.height-48 {
  height: 48px;
}

.float-l {
  float: left;
}

.float-r {
  float: right;
}

input {
  font-size: 14px;
  font-weight: normal;
}

input::placeholder {
  font-weight: normal;
  color: #999;
}

.submit-btn {
  //width: 100%;
  background: #2c5de5;
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  box-sizing: border-box;
  padding: 8px 16px;
  text-align: center;

  &:active {
    opacity: 0.8;
  }
}

.normal-btn {
  background: #fff;
  border: 1px solid #2c5de5;
  color: #2c5de5;

  //width: 100%;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
  padding: 8px 16px;
  text-align: center;
}

// // 背景颜色
// .bgc-primary-5 {
//   background-color: @primary-5;
// }

// .bgc-primary-6 {
//   background-color: @primary-6;
// }

// .bgc-white {
//   background-color: @white-8;
// }

// .bgc-black {
//   background-color: @gray-8;
// }

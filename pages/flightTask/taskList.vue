<template>
  <view class="receive-page page-bg">
    <!-- 自定义导航栏 -->
    <CustomerNav
      :title="pageType === 1 ? '待确认任务' : '任务列表'"
      custom-go-back
      @onBack="onBack"
    />
    <!--筛选    -->
    <view class="padding-left-16 padding-right-16">
      <FilterForm
        :page-type="pageType === 1 ? 'taskList1' : 'taskList2'"
        :default-filter="filterForm"
        @search="getData"
      >
        <template v-slot:options>
          <view class="submit-btn" @click="addTask()" v-if="pageType === 2"
            >新增
          </view>
        </template>
      </FilterForm>
    </view>
    <!-- 任务列表 -->
    <view class="task-list" v-if="taskList.length > 0">
      <TaskItem
        v-for="(task, index) in taskList"
        :key="index"
        :task-item="task"
        :pageType="pageType"
        @refresh="getData"
      />
    </view>
    <Empty v-else />

    <van-dialog id="van-dialog" />
    <Background />
  </view>
</template>

<script>
import CustomerNav from '../../components/CutomerNav/index.vue'
import { queryFlightTaskConfig } from '../../api/flightTask'
import { getRelativeDateText } from '../../utils'
import Empty from '../../components/Empty/index.vue'
import Background from '../../components/Background/index.vue'
import TaskItem from './compoents/TaskItem.vue'
import FormItem from '../flightTask--copy/compoents/FormItem.vue'
import FilterForm from './compoents/FilterForm.vue'

export default {
  name: 'taskList',
  components: {
    FilterForm,
    FormItem,
    TaskItem,
    Background,
    Empty,
    CustomerNav,
  },
  data() {
    return {
      pageType: 2, //1:未确认, 2:所有
      taskList: [],
      filterForm: {
        selectedDateType: '',
        flightDate: '',
        aircraftType: '',
        aircraftTypeText: '',
        releaseType: 2,
        taskStatus: 0,
      },
    }
  },
  onLoad: function (options) {
    this.pageType = Number(options.pageType) || 1
  },
  onShow() {
    this.getData(this.filterForm)
  },
  onPullDownRefresh() {
    this.filterForm = {
      selectedDateType: '',
      flightDate: '',
      aircraftType: '',
      aircraftTypeText: '',
      releaseType: 2,
      taskStatus: null,
    }
    this.getData(this.filterForm).finally(() => {
      uni.stopPullDownRefresh()
    })
  },
  methods: {
    getRelativeDateText,
    onBack() {
      uni.switchTab({
        url: '/pages/home/<USER>',
      })
    },
    // 加载任务列表
    async getData(filterForm) {
      if (filterForm) {
        this.filterForm = filterForm
      }
      const params = {
        aircraftType: this.filterForm.aircraftType,
        flightDate: this.filterForm.flightDate,
        releaseType: this.filterForm.releaseType,
        taskStatus: this.filterForm.taskStatus,
      }
      if (this.pageType === 2) {
        params.taskStatus = null
      }
      const res = await queryFlightTaskConfig(params)
      if (res.response.code === 200) {
        this.taskList = res.response.data || []
      }
    },
    //新增任务
    addTask() {
      uni.navigateTo({
        url: '/pages/flightTask/create',
      })
    },
  },
}
</script>

<style scoped lang="scss">
@import '../../assets/css/common.less';

.receive-page {
  min-height: 100vh;
}

.task-list {
  padding: 0 16px 16px 16px;
}

.input-box-line {
  //padding: 4px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 8px;
  gap: 8px;
  color: #666;
  font-size: 14px;

  .right-icon {
    font-size: 12px;
    color: #999;
  }

  input {
    width: 100%;
  }
}

.submit-btn {
  flex: 1;
  //background: #2c5de5;
  //border-radius: 4px;
  //color: #fff;
  //font-size: 14px;
  //box-sizing: border-box;
  //padding: 8px 16px;
  //margin-top: 16px;
  //text-align: center;
  ////margin-left: 50%;
  ////transform: translateX(-50%);
  //
  //&:active {
  //  opacity: 0.8;
  //}
}

.normal-btn {
  background: #fff;
  border: 1px solid #2c5de5;
  color: #2c5de5;
}
</style>
